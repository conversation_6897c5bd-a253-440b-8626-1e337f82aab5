import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { 
  FileArtifact, 
  HtmlArtifact, 
  MarkdownArtifact, 
  RecordArtifact, 
  NodeResourcesArtifact ,
  SlidesArtifact
} from '@bika/domains/ai-artifacts';
// import { SlidesArtifact } from '@bika/domains/ai-artifacts/slides-server-artifact/slides-artifact';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import type { NodeResource } from '@bika/types/node/bo';
import { FieldVO, RecordVO } from '@bika/types/database/vo';
import { Slides, SlidesOutline } from '@bika/types/ai/vo';
import { FC } from 'react';
import { Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/text-components';

// Mock data for SkillsetSelectDTO
const mockSkillsets: SkillsetSelectDTO[] = [
  { kind: 'preset', key: 'default' },
  { kind: 'preset', key: 'bika-ai-page' },
  { kind: 'preset', key: 'bika-database' },
];

// Mock data for ToolInvocation
const mockToolInvocation: ToolInvocation = {
  toolName: 'generate_artifact',
  toolCallId: 'artifact-call-id',
  state: 'result',
  args: {
    prompt: 'Generate artifact',
  },
  result: {},
};

// Mock data for each artifact type
const mockHtmlContent = `
<div style="padding: 20px; font-family: Arial, sans-serif; background: #f8f9fa; border-radius: 8px;">
  <h1 style="color: #2c3e50;">Sample HTML Artifact</h1>
  <p>This is a sample HTML artifact demonstrating rich content rendering.</p>
  <button style="background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px;">
    Interactive Button
  </button>
</div>
`;

const mockMarkdownContent = `# Sample Markdown Artifact

This is a **sample markdown** artifact with various formatting:

## Features
- **Bold text** and *italic text*
- Code snippets
- Lists and more

\`\`\`javascript
console.log('Hello, World!');
\`\`\`

> This is a blockquote example.
`;

const mockFields: FieldVO[] = [
  {
    id: 'field1',
    name: 'Name',
    type: 'SingleLineText',
    property: {},
    isPrimary: true,
  },
  {
    id: 'field2',
    name: 'Email',
    type: 'Email',
    property: {},
    isPrimary: false,
  },
];

const mockRecords: RecordVO[] = [
  {
    id: 'rec1',
    databaseId: 'db1',
    cells: {
      field1: {
        id: 'field1',
        value: 'John Doe',
      },
      field2: {
        id: 'field2',
        value: '<EMAIL>',
      },
    },
  },
];

const mockSlides: Slides = [
  {
    slide_number: 1,
    title: 'Sample Slide',
    type: 'title',
    html_content: `
      <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center;">
        <h1 style="font-size: 2.5rem; margin-bottom: 20px;">Sample Presentation</h1>
        <p style="font-size: 1.2rem;">Generated by AI</p>
      </div>
    `,
  },
];

const mockSlidesOutline: SlidesOutline = {
  userPrompt: 'Create a sample presentation about AI technology with a modern, professional design',
  outline: {
    title: 'Sample Presentation',
    slides: [
      {
        slide_number: 1,
        slide_title: 'Sample Slide',
        slide_type: 'title',
        slide_data: {
          subtitle: 'Generated by AI',
          author: 'AI System',
          date: 'December 2023',
        },
        description: 'Title slide for sample presentation',
      },
    ],
  },
  slide_template: {
    theme: {
      color_primary: '#667eea',
      color_secondary: '#764ba2',
      color_text: '#2c3e50',
      color_accent: '#3498db',
      color_background: '#f8f9fa',
      color_surface: '#ffffff',
    },
    template: `# HTML Template Creative Mandate

## 🎯 Your Mission
Your role is that of a **world-class presentation designer**. Your goal is to craft a **visually stunning, professional, and modern** single-page HTML slide. The design must be creative, memorable, and impactful.

## 🎨 Aesthetic Principles (Crucial for a beautiful result)
1.  **Visually Stunning & Evocative Design**:
    *   **AVOID** generic, bland, or boring corporate templates.
    *   **AIM FOR** the quality of a keynote from a major tech conference (e.g., Apple, Google I/O) or a top-rated design on Dribbble/Behance.
    *   Embrace bold, elegant, or minimalist aesthetics. The design should feel intentional and sophisticated.

2.  **Typographic Excellence**:
    *   Treat typography as a primary design element. Use modern, readable Google Fonts.
    *   Establish a clear visual hierarchy with font sizes, weights, and styles.
    *   Don't be afraid to use large, impactful headings or sophisticated font pairings.

3.  **The Background as a Masterpiece: Your Primary Decorative Canvas**
    *   **THIS IS THE MOST CRITICAL PRINCIPLE.** The slide's background is **NOT** a passive container. It **IS** the primary visual artwork. Your main creative effort must be focused on making the background a stunning and decorative piece in its own right.
    *   **MANDATORY DECORATIVE TECHNIQUE:** You **MUST** use CSS to create bold, abstract, and irregular shapes directly on the \`<body>\` or its pseudo-elements (\`::before\`, \`::after\`). This is the designated method for adding visual impact and character.
    *   **IMPLEMENTATION STYLES (Choose one or combine):**
        *   **Fluid Organic Forms (Blobs):** Generate large, soft-edged, overlapping liquid shapes. Use stacked \`radial-gradient\`s or extreme, non-uniform \`border-radius\` to create a sense of depth and fluidity. This is great for modern, approachable designs.
        *   **Geometric Shards & Edges:** Use \`clip-path: polygon()\` to create sharp, asymmetrical, and overlapping geometric fragments. This style evokes a feeling of technology, precision, and high energy.
    *   **AVOID AT ALL COSTS:**
        *   A simple, single-color background.
        *   A basic two-color linear gradient.
        *   Any background that looks like a generic, default template.
    *   The purpose of this background art is to create **dynamic composition, visual hierarchy, and a memorable identity** for the slide. Content containers placed on top should be mostly transparent to proudly display the masterpiece underneath.

## 🛠️ Core Technical Directives (Must be followed)
1.  **Body-First Rendering**: The \`<body>\` element is your canvas for all background art. Content layers on top with proper \`z-index\`.
2.  **Iframe-Friendly & Responsive Layout**:
    *   Slide height is determined by content (\`height: auto\`) with a reasonable \`min-height\`.
    *   **STRICTLY FORBIDDEN**: Do not use viewport units (\`vh\`, \`vw\`). They break iframe embedding.
    *   Use generous \`padding\` (e.g., \`3rem\` to \`5rem\`) for internal spacing.
3.  **Theming with CSS Variables**: Strictly use the CSS variables defined in the \`theme\` object for all colors to ensure consistency.

## 📚 Resources & Tech Stack
*   **CSS Framework**: Tailwind CSS
*   **Charting Library**: ECharts (for 'chart' type slides)
*   **Icons**: Font Awesome
*   **Fonts**: Google Fonts
*   Include all resources via CDN links in the HTML \`<head>\`.

## 📊 ECharts Guidelines
*   For 'chart' type slides, you **must** use ECharts to render an interactive, responsive chart.
*   The chart's theme and colors must align beautifully with the overall slide design. Use the \`chart_config.color_scheme\` or derive colors from the main theme.
`,
  },
};

const mockNodeResources: NodeResource[] = [
  {
    resourceType: 'DATABASE',
    id: 'db-1',
    name: 'Sample Database',
    description: 'A sample database resource',
    databaseType: 'DATUM',
    fields: [
      {
        name: 'Title',
        type: 'SingleLineText',
        id: 'field-1',
      },
    ],
  },
];

const AllArtifactsStory: FC = () => {
  return (
    <Box sx={{ p: 4, display: 'flex', flexDirection: 'column', gap: 6 }}>
      <Typography level="h1" sx={{ textAlign: 'center', mb: 4 }}>
        All AI Artifacts Showcase
      </Typography>

      {/* File Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          File Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <FileArtifact
            filePath="https://via.placeholder.com/600x400/4CAF50/FFFFFF?text=Sample+File"
            content="Sample file content"
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_file',
            }}
          />
        </Box>
      </Box>

      {/* HTML Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          HTML Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <HtmlArtifact
            content={mockHtmlContent}
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_html',
            }}
            data={{ html: mockHtmlContent }}
          />
        </Box>
      </Box>

      {/* Markdown Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          Markdown Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <MarkdownArtifact
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_markdown',
            }}
            value={{
              content: mockMarkdownContent,
              title: 'Sample Markdown',
            }}
          />
        </Box>
      </Box>

      {/* Record Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          Record Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <RecordArtifact
            data={mockRecords}
            fields={mockFields}
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_records',
            }}
          />
        </Box>
      </Box>

      {/* Slides Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          Slides Artifact
        </Typography>
        <Box sx={{ height: '500px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <SlidesArtifact
            slides={mockSlides}
            outline={mockSlidesOutline}
          />
        </Box>
      </Box>

      {/* Node Resources Artifact */}
      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>
          Node Resources Artifact
        </Typography>
        <Box sx={{ height: '400px', border: '1px solid #ddd', borderRadius: '8px', overflow: 'hidden' }}>
          <NodeResourcesArtifact
            resources={mockNodeResources}
            skillsets={mockSkillsets}
            tool={{
              ...mockToolInvocation,
              toolName: 'generate_node_resources',
            }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default {
  title: '@bika/ai/_AllArtifacts',
  component: AllArtifactsStory,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof AllArtifactsStory>;

type Story = StoryObj<typeof AllArtifactsStory>;

export const AllArtifacts: Story = {};
